# ========================================
# 服务器配置
# ========================================
# 服务端口
server.port=8080
# 服务上下文路径
server.servlet.context-path=/notification-service

# ========================================
# Spring应用配置
# ========================================
# 应用名称（用于服务注册）
spring.application.name=notification-service

# ========================================
# Nacos服务发现配置
# ========================================
# Nacos服务器地址
spring.cloud.nacos.discovery.server-addr=127.0.0.1:8848
# 服务注册的命名空间（可选）
spring.cloud.nacos.discovery.namespace=
# 服务注册的分组（可选）
spring.cloud.nacos.discovery.group=DEFAULT_GROUP
# 是否启用服务注册
spring.cloud.nacos.discovery.enabled=true
# 服务实例的IP地址（可选，默认自动获取）
# spring.cloud.nacos.discovery.ip=127.0.0.1
# 服务实例的端口（可选，默认使用server.port）
# spring.cloud.nacos.discovery.port=8080

# ========================================
# 数据库配置
# ========================================
# 数据库URL（这里使用阿里云RDS）
spring.datasource.url=***********************************************************************************************************************************
# 数据库用户名
spring.datasource.username=alex
# 数据库密码
spring.datasource.password=Yishao@112
# 数据库驱动
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# ========================================
# 连接池配置（HikariCP）
# ========================================
# 最大连接池大小
spring.datasource.hikari.maximum-pool-size=20
# 最小空闲连接数
spring.datasource.hikari.minimum-idle=5
# 连接超时时间（毫秒）
spring.datasource.hikari.connection-timeout=30000
# 空闲连接最大存活时间
spring.datasource.hikari.idle-timeout=600000
# 连接最大生命周期
spring.datasource.hikari.max-lifetime=1800000

# ========================================
# MyBatis Plus 配置
# ========================================
# 开启驼峰命名转换（数据库字段转Java属性）
mybatis-plus.configuration.map-underscore-to-camel-case=true
# 开启SQL语句打印（开发环境使用）
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
# 逻辑删除字段
mybatis-plus.global-config.db-config.logic-delete-field=deleted
# 逻辑删除值（已删除）
mybatis-plus.global-config.db-config.logic-delete-value=1
# 逻辑删除值（未删除）
mybatis-plus.global-config.db-config.logic-not-delete-value=0

# ========================================
# 日志配置
# ========================================
# 设置日志级别（DEBUG/INFO/WARN/ERROR）
logging.level.com.enterprise.notification=DEBUG
# MyBatis Plus日志级别
logging.level.com.baomidou.mybatisplus=DEBUG
# 控制台日志格式
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n

# ========================================
# Swagger API文档配置
# ========================================
# API文档JSON路径
springdoc.api-docs.path=/api-docs
# Swagger UI访问路径
springdoc.swagger-ui.path=/swagger-ui.html
# API操作按方法排序
springdoc.swagger-ui.operationsSorter=method
# API标签按字母排序
springdoc.swagger-ui.tagsSorter=alpha
# 启用"试用"功能
springdoc.swagger-ui.tryItOutEnabled=true
# 启用过滤器
springdoc.swagger-ui.filter=true

# ========================================
# 通知服务配置
# ========================================

# ----------------------------------------
# 异步配置
# ----------------------------------------
# 异步线程池核心线程数
notification.async.core-pool-size=5
# 异步线程池最大线程数
notification.async.max-pool-size=20
# 异步线程池队列容量
notification.async.queue-capacity=100
# 线程空闲时间（秒）
notification.async.keep-alive-seconds=60
# 线程名前缀
notification.async.thread-name-prefix=NotificationAsync-

# ----------------------------------------
# 短信服务配置
# ----------------------------------------
# 阿里云短信服务
# 是否启用阿里云短信
notification.providers.sms.aliyun.enabled=true
# 阿里云AccessKey ID（请替换为实际值）
notification.providers.sms.aliyun.access-key-id=your_access_key_id
# 阿里云AccessKey Secret（请替换为实际值）
notification.providers.sms.aliyun.access-key-secret=your_access_key_secret
# 短信签名（请替换为实际值）
notification.providers.sms.aliyun.sign-name=your_sign_name
# 阿里云短信端点
notification.providers.sms.aliyun.endpoint=https://dysmsapi.aliyuncs.com

# 腾讯云短信服务
# 是否启用腾讯云短信
notification.providers.sms.tencent.enabled=false
# 腾讯云SecretId（请替换为实际值）
notification.providers.sms.tencent.secret-id=your_secret_id
# 腾讯云SecretKey（请替换为实际值）
notification.providers.sms.tencent.secret-key=your_secret_key
# 腾讯云应用ID（请替换为实际值）
notification.providers.sms.tencent.app-id=your_app_id

# ----------------------------------------
# 邮件服务配置（第三方）
# ----------------------------------------
# AWS SES邮件服务
# 注意：AWS SES需要单独配置，如果不使用可以只配置SMTP邮件服务
notification.providers.email.aws-ses.enabled=false
# AWS访问密钥（请替换为实际值）
notification.providers.email.aws-ses.access-key=your_aws_access_key
# AWS秘密密钥（请替换为实际值）
notification.providers.email.aws-ses.secret-key=your_aws_secret_key
# AWS区域
notification.providers.email.aws-ses.region=us-east-1
# 发件人邮箱
notification.providers.email.aws-ses.from-email=<EMAIL>

# SendGrid邮件服务
# 注意：SendGrid需要单独配置，如果不使用可以只配置SMTP邮件服务
notification.providers.email.sendgrid.enabled=false
# SendGrid API密钥（请替换为实际值）
notification.providers.email.sendgrid.api-key=your_sendgrid_api_key
# 发件人邮箱
notification.providers.email.sendgrid.from-email=<EMAIL>

# ----------------------------------------
# 即时通讯IM配置
# ----------------------------------------
# 企业微信配置
# 是否启用企业微信通知
notification.providers.im.wechat-work.enabled=false
# 企业微信ID（请替换为实际值）
notification.providers.im.wechat-work.corp-id=your_corp_id
# 企业微信Secret（请替换为实际值）
notification.providers.im.wechat-work.corp-secret=your_corp_secret
# 企业应用ID（请替换为实际值）
notification.providers.im.wechat-work.agent-id=your_agent_id

# 钉钉配置
# 是否启用钉钉通知
notification.providers.im.dingtalk.enabled=false
# 钉钉Key（请替换为实际值）
notification.providers.im.dingtalk.app-key=your_app_key
# 钉钉Secret（请替换为实际值）
notification.providers.im.dingtalk.app-secret=your_app_secret

# ----------------------------------------
# 模板配置
# ----------------------------------------
# 模板变量匹配模式（如：${userName}）
notification.template.variable-pattern=\\$\\{([^}]+)\\}
# 是否启用模板缓存
notification.template.cache-enabled=true
# 缓存TTL（秒）
notification.template.cache-ttl=3600

# ----------------------------------------
# 重试配置
# ----------------------------------------
# 最大重试次数
notification.retry.max-attempts=3
# 重试间隔（秒）
notification.retry.delay-seconds=5

# ========================================
# SMTP邮件服务器配置
# ========================================
# SMTP服务器配置（支持各种邮件服务商）
# 常用配置：
# Gmail: smtp.gmail.com
# QQ邮箱: smtp.qq.com
# 163邮箱: smtp.163.com
# 企业邮箱: 请咨询管理员
spring.mail.host=smtp.gmail.com
# SMTP端口（587用于TLS，465用于SSL，25用于普通）
spring.mail.port=587
# 邮箱用户名（完整邮箱地址）
spring.mail.username=<EMAIL>
# 邮箱密码（Gmail等需要使用应用专用密码）
spring.mail.password=your_app_password
# 启用SMTP认证
spring.mail.properties.mail.smtp.auth=true
# 启用STARTTLS加密
spring.mail.properties.mail.smtp.starttls.enable=true
# 要求STARTTLS加密
spring.mail.properties.mail.smtp.starttls.required=true
# 信任的SMTP服务器（SSL证书验证）
spring.mail.properties.mail.smtp.ssl.trust=smtp.gmail.com

# ========================================
# 邮件通知基础配置
# ========================================
# 是否启用邮件通知
notification.email.enabled=true
# 默认发件人邮箱（需要与SMTP配置一致）
notification.email.from=<EMAIL>
# 发件人显示名称
notification.email.from-name=Notification Platform
